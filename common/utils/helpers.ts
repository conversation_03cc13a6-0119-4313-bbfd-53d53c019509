import {
  pinataUploadUrl,
  revalidateUrl,
} from "@/common/utils/network/endpoints";
import {
  ClassValue, clsx,
} from "clsx";
import { twMerge } from "tailwind-merge";

export function cn (...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const camelToSnakeCase = (propertyName: string) => propertyName
  .replace(
    /[A-Z]/g,
    (letter) => `_${letter.toLowerCase()}`,
  );


// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const parseObjectPropertiesToSnakeCase = (object: any): any => {
  return Object.fromEntries(
    Object.entries(object).map(([key, value]) => {
      if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        return [camelToSnakeCase(key), value];
      }
      const parsedNestedObject = parseObjectPropertiesToSnakeCase(value);
      return [camelToSnakeCase(key), parsedNestedObject];
    }),
  );
};

export const createFileFormData = async (
  content: string,
  filename: string,
): Promise<FormData> => {
  const blob = new Blob([content], { type: 'image/svg+xml' });
  const file = new File([blob], filename);
  const formData = new FormData();
  formData.set('file', file);
  return formData;
};

export const uploadToPinata = async (formData: FormData): Promise<string> => {
  const response = await fetch(pinataUploadUrl, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Pinata upload failed: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  if (!data) {
    throw new Error('No data received from Pinata');
  }

  return data;
};

export const isMobileDevice = typeof navigator !== 'undefined' && /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)

export const revalidateTagData = async (tagName: string) => {
  await fetch(revalidateUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      tagName,
    }),
  })
}

export const extractTwitterUsername = (url: string) => {
  if (!url) {
    return null;
  }
  const pattern = /^(?:https?:\/\/)?(?:www\.)?(?:twitter\.com|x\.com)\/([a-zA-Z0-9_]+)(?:\/.*)?$/;

  const match = url.match(pattern);
  return match ? match[1] : null;
};

export const getPath = (filepath: string) => {
  const normalizedPath = filepath.replace(/\\/g, '/');
  const filename = normalizedPath.split('/').pop();
  return `${process.env.NEXT_PUBLIC_AGENT_URL || ''}/media/generated/${filename}`;
}
