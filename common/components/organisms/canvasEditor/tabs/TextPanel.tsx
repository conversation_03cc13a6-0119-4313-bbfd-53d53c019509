'use client'

import React, {
  useCallback,
  useEffect, useState,
} from 'react';
import Konva from 'konva';
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
} from 'lucide-react';
import toast from 'react-hot-toast';
import { Button } from '../../../atoms';
import { Dropdown } from '../../../molecules';
import { addCursorHandlers } from '../CanvasEditor';

interface TextPanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}

export const TextPanel = ({
  canvas,
}: TextPanelProps) => {
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontSize, setFontSize] = useState(24);
  const [textColor, setTextColor] = useState('#000000');
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [textAlign, setTextAlign] = useState('center');


  const fonts = [
    'Arial',
    'Brush Script MT',
    'Chillax',
    'Courier New',
    'Georgia',
    'Impact',
    'Tahoma',
    'Trebuchet MS',
    'Verdana',
  ];

  const startTextEditing = useCallback((textNode: Konva.Text) => {
    if (!canvas) {
      return;
    }
    const stage = canvas;
    const layer = textNode.getLayer();

    const transformer = stage.findOne('Transformer') as Konva.Transformer;
    if (transformer) {
      transformer.nodes([]);
      transformer.getLayer()?.batchDraw();
    }

    const originalOpacity = textNode.opacity();
    textNode.opacity(0);
    textNode.visible(false);
    layer?.batchDraw();

    const textPosition = textNode.getAbsolutePosition();
    const stageBox = stage.container().getBoundingClientRect();

    const textarea = document.createElement('textarea');
    document.body.appendChild(textarea);

    textarea.value = textNode.text();
    textarea.style.position = 'absolute';
    textarea.style.top = (textPosition.y + stageBox.top) + 'px';
    textarea.style.left = (textPosition.x + stageBox.left) + 'px';
    textarea.style.width = Math.max(textNode.width() + 20, 100) + 'px';
    textarea.style.height = Math.max(textNode.height() + 10, 30) + 'px';
    textarea.style.fontSize = textNode.fontSize() + 'px';
    textarea.style.fontFamily = textNode.fontFamily();
    textarea.style.fontWeight = textNode.fontStyle().includes('bold') ? 'bold' : 'normal';
    textarea.style.fontStyle = textNode.fontStyle().includes('italic') ? 'italic' : 'normal';
    textarea.style.textAlign = textNode.align();
    textarea.style.border = '2px solid #007bff';
    textarea.style.padding = '4px';
    textarea.style.margin = '0px';
    textarea.style.overflow = 'hidden';
    textarea.style.background = 'white';
    const fillValue = textNode.fill();
    textarea.style.color = typeof fillValue === 'string' ? fillValue : '#000000';
    textarea.style.outline = 'none';
    textarea.style.resize = 'none';
    textarea.style.lineHeight = textNode.lineHeight().toString();
    textarea.style.transformOrigin = 'left top';
    textarea.style.zIndex = '9999';
    textarea.style.opacity = '1';
    textarea.style.visibility = 'visible';
    textarea.style.pointerEvents = 'auto';

    textarea.focus();
    textarea.select();

    let isEditing = true;

    const cleanup = () => {
      if (!isEditing) {
        return;
      }
      isEditing = false;

      textNode.opacity(originalOpacity);
      textNode.visible(true);
      layer?.batchDraw();

      textarea.removeEventListener('blur', finishEditing);
      textarea.removeEventListener('keydown', handleKeyDown);

      try {
        if (textarea.parentNode) {
          textarea.parentNode.removeChild(textarea);
        }
      } catch (error) {
        console.warn('Error removing textarea:', error);
      }
    };

    const finishEditing = () => {
      if (!isEditing) {
        return;
      }
      textNode.text(textarea.value);

      const transformer = stage.findOne('Transformer') as Konva.Transformer;
      if (transformer) {
        transformer.nodes([textNode]);
        transformer.getLayer()?.batchDraw();
      }

      // Dispatch event to save history state after text editing
      window.dispatchEvent(new CustomEvent('canvasTextEditComplete'));

      cleanup();
    };

    const cancelEditing = () => {
      if (!isEditing) {
        return;
      }
      cleanup();
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        finishEditing();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        cancelEditing();
      }
    };

    textarea.addEventListener('keydown', handleKeyDown);
    textarea.addEventListener('blur', finishEditing);
  }, [canvas]);

  const addText = () => {
    if (!canvas) {
      toast.error('Canvas not available');
      return;
    }

    try {
      let layer = canvas.getLayers()[0] as Konva.Layer;

      if (!layer) {
        layer = new Konva.Layer();
        canvas.add(layer);
      }

      const text = new Konva.Text({
        x: 100,
        y: 100,
        text: 'Double-click to edit',
        fontFamily: fontFamily,
        fontSize: fontSize,
        fill: textColor,
        fontStyle: `${isBold ? 'bold' : 'normal'} ${isItalic ? 'italic' : 'normal'}`,
        align: textAlign as 'left' | 'center' | 'right',
        draggable: true,
      });

      addCursorHandlers(text);

      text.on('dblclick', () => {
        startTextEditing(text);
      });

      text.on('click', () => {
        const layer = text.getLayer();
        if (!layer) {
          return;
        }
        let transformer = layer.findOne('Transformer') as Konva.Transformer;
        if (!transformer) {
          transformer = new Konva.Transformer();
          layer.add(transformer);
        }

        transformer.nodes([text]);
        canvas.batchDraw();
      });

      layer.add(text);

      let transformer = layer.findOne('Transformer') as Konva.Transformer;
      if (!transformer) {
        transformer = new Konva.Transformer();
        layer.add(transformer);
      }

      transformer.nodes([text]);
      layer.batchDraw();

      // Dispatch event to save history state
      window.dispatchEvent(new CustomEvent('canvasElementAdded'));

      toast.success('Text added to canvas!');
    } catch (error) {
      console.error('Error adding text:', error);
      toast.error('Failed to add text to canvas');
    }
  };

  const updateSelectedText = useCallback(() => {
    if (!canvas) {
      return;
    }

    const transformer = canvas.findOne('Transformer') as Konva.Transformer;
    if (transformer) {
      const selectedNodes = transformer.nodes();
      selectedNodes.forEach((node) => {
        if (node.getClassName() === 'Text') {
          const textNode = node as Konva.Text;
          textNode.fontFamily(fontFamily);
          textNode.fontSize(fontSize);
          textNode.fill(textColor);
          textNode.fontStyle(`${isBold ? 'bold' : 'normal'} ${isItalic ? 'italic' : 'normal'}`);
          textNode.align(textAlign as 'left' | 'center' | 'right');
        }
      });
      canvas.batchDraw();
    }
  }, [canvas, fontFamily, fontSize, textColor, isBold, isItalic, textAlign]);

  useEffect(() => {
    updateSelectedText();
  }, [fontFamily, fontSize, textColor, isBold, isItalic, textAlign, updateSelectedText]);

  useEffect(() => {
    if (!canvas) {
      return;
    } 

    const addEventHandlersToTextNodes = () => {
      const layers = canvas.getLayers();
      layers.forEach((layer) => {
        layer.find('Text').forEach((node) => {
          const textNode = node as Konva.Text;

          textNode.off('dblclick');
          textNode.off('click');

          addCursorHandlers(textNode);

          textNode.on('dblclick', () => {
            startTextEditing(textNode);
          });

          textNode.on('click', () => {
            const textLayer = textNode.getLayer();
            if (!textLayer) {
              return;
            }
            let transformer = textLayer.findOne('Transformer') as Konva.Transformer;
            if (!transformer) {
              transformer = new Konva.Transformer();
              textLayer.add(transformer);
            }

            transformer.nodes([textNode]);
            canvas.batchDraw();
          });
        });
      });
    };

    addEventHandlersToTextNodes();

    const handleCanvasChange = () => {
      setTimeout(addEventHandlersToTextNodes, 100);
    };

    canvas.on('dragend', handleCanvasChange);
    canvas.on('transformend', handleCanvasChange);

    return () => {
      canvas.off('dragend', handleCanvasChange);
      canvas.off('transformend', handleCanvasChange);
    };
  }, [canvas, startTextEditing]);

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Add Text</h3>
        <p className="text-gray-400 text-sm">Add and customize text</p>
      </div>
      <div className="space-y-6">
        <div className="space-y-4">
          <div>
            <Dropdown
              label="Font Family"
              options={fonts.map(font => ({ 
                value: font, 
                label: font,
              }))}
              selectedOption={{ 
                label: fontFamily, 
                option: fontFamily,
              }}
              onSelect={(option) => {
                setFontFamily(option.label)
              }}
              placeholder="Select Font Style"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Font Size: {fontSize}px
            </label>
            <input
              type="range"
              min="12"
              max="120"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-full accent-violets-are-blue"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Color</label>
            <input
              type="color"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-full h-10 rounded-lg p-0.5 py-0 border border-neutral-600"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Style</label>
            <div className="flex gap-2">
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsBold(!isBold)}
                className={`${isBold ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Bold
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsItalic(!isItalic)}
                className={`${isItalic ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Italic
              </Button>
            </div>
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Alignment</label>
            <div className="flex gap-2">
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('left')}
                className={`${textAlign === 'left' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Left"
              >
                <AlignLeft size={16} />
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('center')}
                className={`${textAlign === 'center' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Center"
              >
                <AlignCenter size={16} />
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('right')}
                className={`${textAlign === 'right' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Right"
              >
                <AlignRight size={16} />
              </Button>
            </div>
          </div>
        </div>
        <Button
          variant="gradient"
          size="md"
          width="w-full"
          onClick={addText}
        >
          Add Text
        </Button>
      </div>
    </div>
  );
};
